<!-- 结算人员与运营商维护 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['xdtCharge:staffMaintenance:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['xdtCharge:staffMaintenance:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['xdtCharge:staffMaintenance:import']"
          >导入</el-button
        >
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/xdt/charge/staffMaintenance.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";

export default {
  name: "staffMaintenance",
  mixins: [exportMixin],
  data() {
    return {
      workLoading: false,
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      // 下拉选项数据
      statusOptions: [],
      typeOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取字典数据
    this.getDicts("staff_status").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("operator_type").then((response) => {
      this.typeOptions = response.data;
    });
  },
  methods: {
    checkPermission,

    handleBatchAdd() {
      this.$refs.crud.switchModalView(true, "add");
    },

    handleBatchImport() {
      // 导入功能实现
      console.log("导入功能");
    },

    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleCommonExport(api.export, params);
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");

      try {
        if (crudOperationType === "add") {
          await api.add(params);
          this.$message.success("新增成功");
        } else if (crudOperationType === "edit") {
          await api.update(params);
          this.$message.success("更新成功");
        }
        this.loadData();
      } catch (error) {
        console.error("操作失败:", error);
        return false;
      }
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "institutionName",
          title: "机构名称",
          width: 150,
        },
        {
          field: "operatorName",
          title: "运营商",
          width: 120,
        },
        {
          field: "staffName",
          title: "负责人",
          width: 100,
        },
        {
          field: "contactInfo",
          title: "联系方式",
          width: 120,
        },
        {
          field: "status",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            const statusMap = {
              "0": "正常",
              "1": "停用",
            };
            return statusMap[cellValue] || cellValue;
          },
        },
        {
          field: "createBy",
          title: "创建人",
          width: 100,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 160,
        },
        {
          field: "updateBy",
          title: "更新人",
          width: 100,
        },
        {
          field: "updateTime",
          title: "更新时间",
          width: 160,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 4, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "institutionName",
            element: "el-input",
            title: "机构名称",
            props: {
              placeholder: "请输入机构名称",
            },
          },
          {
            field: "operatorName",
            element: "el-input",
            title: "运营商",
            props: {
              placeholder: "请输入运营商名称",
            },
          },
          {
            field: "staffName",
            element: "el-input",
            title: "负责人",
            props: {
              placeholder: "请输入负责人",
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
              placeholder: "请选择状态",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        viewBtn: true,
        submitBtn: true,
        okText: "确认",
        cancelText: "取消",
        addBtn: true,
        editBtn: true,
        delBtn: true,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "600px",
        formConfig: [
          {
            field: "institutionName",
            title: "机构名称",
            element: "el-input",
            rules: [{ required: true, message: "请输入机构名称" }],
            props: {
              placeholder: "请输入机构名称",
            },
          },
          {
            field: "operatorName",
            title: "运营商",
            element: "el-input",
            rules: [{ required: true, message: "请输入运营商名称" }],
            props: {
              placeholder: "请输入运营商名称",
            },
          },
          {
            field: "staffName",
            title: "负责人",
            element: "el-input",
            rules: [{ required: true, message: "请输入负责人" }],
            props: {
              placeholder: "请输入负责人",
            },
          },
          {
            field: "contactInfo",
            title: "联系方式",
            element: "el-input",
            rules: [{ required: true, message: "请输入联系方式" }],
            props: {
              placeholder: "请输入联系方式",
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择状态",
            },
            defaultValue: "0",
          },
        ],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 24,
          labelPosition: "right",
          labelWidth: "120px",
        },
      };
    },
  },
};
</script>

<style></style>
