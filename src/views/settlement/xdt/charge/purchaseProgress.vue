<!-- 购电进度 -->
<template>
  <div>
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @loadData="loadData"
    >
      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
            v-has-permi="['xdtCharge:purchaseProgress:export']"
            >导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            >查询
          </el-button>
          <el-button icon="el-icon-refresh" @click.stop="handleReset"
            >重置
          </el-button>
        </div>
      </template>
      <template #toolbar_buttons>
        <el-button
          type="primary"
          @click="handleBatchAdd"
          v-has-permi="['xdtCharge:purchaseProgress:add']"
          >新增</el-button
        >
        <el-button
          type="primary"
          @click="handleBatchImport"
          v-has-permi="['xdtCharge:purchaseProgress:import']"
          >导入</el-button
        >
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import checkPermission from "@/utils/permission.js";
import api from "@/api/settlement/xdt/charge/purchaseProgress.js";
import { initParams } from "@/utils/buse.js";
import exportMixin from "@/mixin/export.js";

export default {
  name: "purchaseProgress",
  mixins: [exportMixin],
  data() {
    return {
      workLoading: false,
      tableTotal: 0,
      // 查询参数
      searchForm: {
        pageNum: 1,
        pageSize: 10,
      },
      visible: false,
      //buse参数-s
      tableProps: {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "id",
          isCurrent: true,
        },
        checkboxConfig: {
          reserve: true,
        },
      },
      tableData: [],
      params: {},
      loading: false,
      pagerProps: {
        layouts: [
          "Total",
          "Sizes",
          "PrevPage",
          "JumpNumber",
          "NextPage",
          "FullJump",
        ],
        background: true,
        className: "pagination-container",
        pageSizes: [10, 20, 30, 50],
      },
      tablePage: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      operationType: "accept",
      //buse参数-e

      // 下拉选项数据
      statusOptions: [],
      typeOptions: [],
    };
  },
  created() {
    this.params = {
      ...initParams(this.filterOptions.config),
    };
    // 获取字典数据
    this.getDicts("purchase_status").then((response) => {
      this.statusOptions = response.data;
    });
    this.getDicts("purchase_type").then((response) => {
      this.typeOptions = response.data;
    });
  },
  methods: {
    checkPermission,

    handleBatchAdd() {
      this.$refs.crud.switchModalView(true, "add");
    },

    handleBatchImport() {
      // 导入功能实现
      console.log("导入功能");
    },

    handleExport() {
      let params = {
        ...this.params,
      };
      this.handleTimeRange(params);
      this.handleCommonExport(api.export, params);
    },

    //处理时间范围参数
    handleTimeRange(params) {
      const arr = [
        {
          field: "purchaseDateRange",
          title: "购电日期",
          startFieldName: "purchaseStartDate",
          endFieldName: "purchaseEndDate",
        },
      ];
      arr.map((x) => {
        if (Array.isArray(params[x.field])) {
          params[x.startFieldName] = params[x.field][0] + " 00:00:00";
          params[x.endFieldName] = params[x.field][1] + " 23:59:59";
          delete params[x.field];
        }
      });
    },

    async loadData() {
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.handleTimeRange(params);
      this.loading = true;
      const res = await api.list(params);
      this.loading = false;
      this.tableData = res.data;
      this.tablePage.total = res.total;
    },

    handleReset() {
      this.tablePage = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.params = initParams(this.filterOptions.config);
      this.loadData();
    },

    handleQuery() {
      this.tablePage.currentPage = 1;
      this.loadData();
    },

    //弹窗确认按钮事件
    async modalConfirmHandler({ crudOperationType, ...formParams }) {
      let params = { ...formParams };
      console.log(crudOperationType, formParams, "提交");

      try {
        if (crudOperationType === "add") {
          await api.add(params);
          this.$message.success("新增成功");
        } else if (crudOperationType === "edit") {
          await api.update(params);
          this.$message.success("更新成功");
        }
        this.loadData();
      } catch (error) {
        console.error("操作失败:", error);
        return false;
      }
    },
  },
  computed: {
    tableColumn() {
      return [
        {
          field: "purchaseMonth",
          title: "结算月份",
          width: 120,
        },
        {
          field: "stationName",
          title: "站点名称",
          width: 150,
        },
        {
          field: "operatorName",
          title: "对方运营商",
          width: 120,
        },
        {
          field: "purchaseAmount",
          title: "对方应得分润",
          width: 120,
        },
        {
          field: "actualAmount",
          title: "实际分润金额",
          width: 120,
        },
        {
          field: "status",
          title: "状态",
          width: 100,
          formatter: ({ cellValue }) => {
            const statusMap = {
              "0": "待处理",
              "1": "已处理",
              "2": "已完成",
            };
            return statusMap[cellValue] || cellValue;
          },
        },
        {
          field: "responsiblePerson",
          title: "负责人",
          width: 100,
        },
        {
          field: "purchaseDate",
          title: "购电日期",
          width: 120,
        },
        {
          field: "remark",
          title: "备注",
          width: 150,
        },
        {
          field: "createBy",
          title: "创建人",
          width: 100,
        },
        {
          field: "createTime",
          title: "创建时间",
          width: 160,
        },
      ];
    },
    filterOptions() {
      return {
        showCount: 5, //默认显示筛选项的个数 多余的展开展示
        layout: "right",
        inline: true,
        labelWidth: "140px",
        //筛选控件配置
        config: [
          {
            field: "purchaseMonth",
            element: "el-input",
            title: "结算月份",
            props: {
              placeholder: "请输入结算月份",
            },
          },
          {
            field: "stationName",
            element: "el-input",
            title: "站点名称",
            props: {
              placeholder: "请输入站点名称",
            },
          },
          {
            field: "operatorName",
            element: "el-input",
            title: "对方运营商",
            props: {
              placeholder: "请输入运营商名称",
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              filterable: true,
              placeholder: "请选择状态",
            },
          },
          {
            field: "purchaseDateRange",
            title: "购电日期",
            element: "el-date-picker",
            props: {
              type: "daterange",
              valueFormat: "yyyy-MM-dd",
              placeholder: "请选择购电日期范围",
            },
          },
          {
            field: "responsiblePerson",
            element: "el-input",
            title: "负责人",
            props: {
              placeholder: "请输入负责人",
            },
          },
        ],
        params: this.params,
      };
    },

    modalConfig() {
      return {
        viewBtn: true,
        submitBtn: true,
        okText: "确认",
        cancelText: "取消",
        addBtn: true,
        editBtn: true,
        delBtn: true,
        menu: true,
        menuWidth: 250,
        menuFixed: "right",
        modalWidth: "800px",
        formConfig: [
          {
            field: "purchaseMonth",
            title: "结算月份",
            element: "el-input",
            rules: [{ required: true, message: "请输入结算月份" }],
            props: {
              placeholder: "请输入结算月份",
            },
          },
          {
            field: "stationName",
            title: "站点名称",
            element: "el-input",
            rules: [{ required: true, message: "请输入站点名称" }],
            props: {
              placeholder: "请输入站点名称",
            },
          },
          {
            field: "operatorName",
            title: "对方运营商",
            element: "el-input",
            rules: [{ required: true, message: "请输入运营商名称" }],
            props: {
              placeholder: "请输入运营商名称",
            },
          },
          {
            field: "purchaseAmount",
            title: "对方应得分润",
            element: "el-input-number",
            rules: [{ required: true, message: "请输入分润金额" }],
            props: {
              precision: 2,
              min: 0,
              placeholder: "请输入分润金额",
            },
          },
          {
            field: "actualAmount",
            title: "实际分润金额",
            element: "el-input-number",
            props: {
              precision: 2,
              min: 0,
              placeholder: "请输入实际分润金额",
            },
          },
          {
            field: "status",
            title: "状态",
            element: "el-select",
            props: {
              options: this.statusOptions,
              optionLabel: "dictLabel",
              optionValue: "dictValue",
              placeholder: "请选择状态",
            },
            defaultValue: "0",
          },
          {
            field: "responsiblePerson",
            title: "负责人",
            element: "el-input",
            props: {
              placeholder: "请输入负责人",
            },
          },
          {
            field: "purchaseDate",
            title: "购电日期",
            element: "el-date-picker",
            props: {
              type: "date",
              valueFormat: "yyyy-MM-dd",
              placeholder: "请选择购电日期",
            },
          },
          {
            field: "remark",
            title: "备注",
            element: "el-input",
            props: {
              type: "textarea",
              rows: 3,
              placeholder: "请输入备注",
            },
          },
        ],
        crudPermission: [],
        customOperationTypes: [],
        formLayoutConfig: {
          defaultColSpan: 12,
          labelPosition: "right",
          labelWidth: "120px",
        },
      };
    },
  },
};
</script>

<style></style>
