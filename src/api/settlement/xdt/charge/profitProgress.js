import request from "@/utils/request";

/**
 * 分润进度管理API接口
 * 提供分润进度信息的增删改查功能
 */
export default {
  /**
   * 分页查询分润进度信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {string} [data.settlementMonth] - 结算月份（模糊查询）
   * @param {string} [data.stationName] - 站点名称（模糊查询）
   * @param {string} [data.operatorName] - 对方运营商（模糊查询）
   * @param {string} [data.status] - 状态（0:待处理 1:已处理 2:已完成）
   * @param {string} [data.settlementStartDate] - 结算开始日期（格式：YYYY-MM-DD HH:mm:ss）
   * @param {string} [data.settlementEndDate] - 结算结束日期（格式：YYYY-MM-DD HH:mm:ss）
   * @param {string} [data.responsiblePerson] - 负责人（模糊查询）
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 分润进度信息列表
   * @returns {string} returns.data[].settlementMonth - 结算月份
   * @returns {string} returns.data[].stationName - 站点名称
   * @returns {string} returns.data[].operatorName - 对方运营商
   * @returns {number} returns.data[].profitAmount - 对方应得分润
   * @returns {number} returns.data[].actualAmount - 实际分润金额
   * @returns {string} returns.data[].status - 状态
   * @returns {string} returns.data[].responsiblePerson - 负责人
   * @returns {string} returns.data[].settlementDate - 结算日期
   * @returns {string} returns.data[].remark - 备注
   * @returns {string} returns.data[].createBy - 创建人
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await profitProgressApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   settlementMonth: '2024-01'
   * });
   */
  list(data) {
    return request({
      url: "/st/xdt/charge/profitProgress/queryPage",
      method: "post",
      data: data,
    });
  },

  /**
   * 新增分润进度信息
   * @param {Object} data - 分润进度信息数据
   * @param {string} data.settlementMonth - 结算月份（必填）
   * @param {string} data.stationName - 站点名称（必填）
   * @param {string} data.operatorName - 对方运营商（必填）
   * @param {number} data.profitAmount - 对方应得分润（必填）
   * @param {number} [data.actualAmount] - 实际分润金额
   * @param {string} [data.status] - 状态
   * @param {string} [data.responsiblePerson] - 负责人
   * @param {string} [data.settlementDate] - 结算日期
   * @param {string} [data.remark] - 备注
   * @returns {Promise<Object>} 返回新增结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string|number} returns.data - 新增记录的ID或操作结果
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 新增分润进度信息
   * const result = await profitProgressApi.add({
   *   settlementMonth: '2024-01',
   *   stationName: '测试站点',
   *   operatorName: '测试运营商',
   *   profitAmount: 1000.00
   * });
   */
  add(data) {
    return request({
      url: "/st/xdt/charge/profitProgress/add",
      method: "post",
      data: data,
    });
  },

  /**
   * 编辑分润进度信息
   * @param {Object} data - 分润进度信息数据
   * @param {number} data.id - 分润进度信息ID（必填）
   * @param {string} [data.settlementMonth] - 结算月份
   * @param {string} [data.stationName] - 站点名称
   * @param {string} [data.operatorName] - 对方运营商
   * @param {number} [data.profitAmount] - 对方应得分润
   * @param {number} [data.actualAmount] - 实际分润金额
   * @param {string} [data.status] - 状态
   * @param {string} [data.responsiblePerson] - 负责人
   * @param {string} [data.settlementDate] - 结算日期
   * @param {string} [data.remark] - 备注
   * @returns {Promise<Object>} 返回编辑结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 编辑分润进度信息
   * const result = await profitProgressApi.update({
   *   id: 1,
   *   actualAmount: 950.00,
   *   status: '1'
   * });
   */
  update(data) {
    return request({
      url: "/st/xdt/charge/profitProgress/edit",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除分润进度信息
   * @param {Object} data - 删除参数
   * @param {number|Array<number>} data.id - 要删除的分润进度信息ID，支持单个ID或ID数组
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除单个分润进度信息
   * const result = await profitProgressApi.delete({ id: 1 });
   * 
   * // 批量删除分润进度信息
   * const result = await profitProgressApi.delete({ id: [1, 2, 3] });
   */
  delete(data) {
    return request({
      url: "/st/xdt/charge/profitProgress/delete",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出分润进度信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {string} [data.settlementMonth] - 结算月份筛选条件
   * @param {string} [data.stationName] - 站点名称筛选条件
   * @param {string} [data.operatorName] - 运营商筛选条件
   * @param {string} [data.status] - 状态筛选条件
   * @param {string} [data.settlementStartDate] - 结算开始日期筛选条件
   * @param {string} [data.settlementEndDate] - 结算结束日期筛选条件
   * @param {string} [data.responsiblePerson] - 负责人筛选条件
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有分润进度信息
   * const result = await profitProgressApi.export({
   *   settlementMonth: '2024-01'
   * });
   */
  export(data) {
    return request({
      url: "/st/xdt/charge/profitProgress/exportExcel",
      method: "post",
      data: data,
    });
  },

  /**
   * 导入分润进度信息Excel文件
   * @param {FormData} data - 包含文件的FormData对象
   * @param {File} data.file - 要导入的Excel文件（必填）
   * @returns {Promise<Object>} 返回导入结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
   * @returns {string} returns.data - 导入结果详情
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导入Excel文件
   * const formData = new FormData();
   * formData.append('file', file);
   * const result = await profitProgressApi.import(formData);
   */
  import(data) {
    return request({
      url: "/st/xdt/charge/profitProgress/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  /**
   * 获取分润进度相关的下拉列表数据
   * 用于表单选择器的数据源
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Object} returns.data - 下拉列表数据对象，key为字段名，value为选项数组
   * @returns {Array<string>} [returns.data.stationNames] - 站点名称选项列表
   * @returns {Array<string>} [returns.data.operatorNames] - 运营商名称选项列表
   * @returns {Array<string>} [returns.data.responsiblePersons] - 负责人选项列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 获取下拉列表数据
   * const result = await profitProgressApi.getDropLists();
   * // result.data = {
   * //   stationNames: ['站点1', '站点2'],
   * //   operatorNames: ['运营商A', '运营商B'],
   * //   responsiblePersons: ['张三', '李四']
   * // }
   */
  getDropLists() {
    return request({
      url: "/st/xdt/charge/profitProgress/getDropLists",
      method: "get",
    });
  },
};
