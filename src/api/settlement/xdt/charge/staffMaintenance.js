import request from "@/utils/request";

/**
 * 结算人员与运营商维护管理API接口
 * 提供结算人员与运营商维护信息的增删改查功能
 */
export default {
  /**
   * 分页查询结算人员与运营商维护信息
   * @param {Object} data - 查询参数
   * @param {number} [data.pageNum=1] - 页码
   * @param {number} [data.pageSize=10] - 每页条数
   * @param {string} [data.institutionName] - 机构名称（模糊查询）
   * @param {string} [data.operatorName] - 运营商（模糊查询）
   * @param {string} [data.staffName] - 负责人（模糊查询）
   * @param {string} [data.status] - 状态（0:正常 1:停用）
   * @returns {Promise<Object>} 返回分页查询结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Array<Object>} returns.data - 结算人员与运营商维护信息列表
   * @returns {string} returns.data[].institutionName - 机构名称
   * @returns {string} returns.data[].operatorName - 运营商
   * @returns {string} returns.data[].staffName - 负责人
   * @returns {string} returns.data[].contactInfo - 联系方式
   * @returns {string} returns.data[].status - 状态
   * @returns {string} returns.data[].createBy - 创建人
   * @returns {string} returns.data[].createTime - 创建时间
   * @returns {string} returns.data[].updateBy - 更新人
   * @returns {string} returns.data[].updateTime - 更新时间
   * @returns {number} returns.pageNum - 当前页码
   * @returns {number} returns.pageSize - 每页条数
   * @returns {number} returns.total - 总记录数
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 查询第一页数据
   * const result = await staffMaintenanceApi.list({
   *   pageNum: 1,
   *   pageSize: 10,
   *   institutionName: '测试机构'
   * });
   */
  list(data) {
    return request({
      url: "/st/xdt/charge/staffMaintenance/queryPage",
      method: "post",
      data: data,
    });
  },

  /**
   * 新增结算人员与运营商维护信息
   * @param {Object} data - 结算人员与运营商维护信息数据
   * @param {string} data.institutionName - 机构名称（必填）
   * @param {string} data.operatorName - 运营商（必填）
   * @param {string} data.staffName - 负责人（必填）
   * @param {string} data.contactInfo - 联系方式（必填）
   * @param {string} [data.status] - 状态
   * @returns {Promise<Object>} 返回新增结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string|number} returns.data - 新增记录的ID或操作结果
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 新增结算人员与运营商维护信息
   * const result = await staffMaintenanceApi.add({
   *   institutionName: '测试机构',
   *   operatorName: '测试运营商',
   *   staffName: '张三',
   *   contactInfo: '13800138000'
   * });
   */
  add(data) {
    return request({
      url: "/st/xdt/charge/staffMaintenance/add",
      method: "post",
      data: data,
    });
  },

  /**
   * 编辑结算人员与运营商维护信息
   * @param {Object} data - 结算人员与运营商维护信息数据
   * @param {number} data.id - 结算人员与运营商维护信息ID（必填）
   * @param {string} [data.institutionName] - 机构名称
   * @param {string} [data.operatorName] - 运营商
   * @param {string} [data.staffName] - 负责人
   * @param {string} [data.contactInfo] - 联系方式
   * @param {string} [data.status] - 状态
   * @returns {Promise<Object>} 返回编辑结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 操作结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 编辑结算人员与运营商维护信息
   * const result = await staffMaintenanceApi.update({
   *   id: 1,
   *   contactInfo: '13900139000',
   *   status: '0'
   * });
   */
  update(data) {
    return request({
      url: "/st/xdt/charge/staffMaintenance/edit",
      method: "post",
      data: data,
    });
  },

  /**
   * 删除结算人员与运营商维护信息
   * @param {Object} data - 删除参数
   * @param {number|Array<number>} data.id - 要删除的结算人员与运营商维护信息ID，支持单个ID或ID数组
   * @returns {Promise<Object>} 返回删除结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 删除结果信息
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 删除单个结算人员与运营商维护信息
   * const result = await staffMaintenanceApi.delete({ id: 1 });
   * 
   * // 批量删除结算人员与运营商维护信息
   * const result = await staffMaintenanceApi.delete({ id: [1, 2, 3] });
   */
  delete(data) {
    return request({
      url: "/st/xdt/charge/staffMaintenance/delete",
      method: "post",
      data: data,
    });
  },

  /**
   * 导出结算人员与运营商维护信息Excel文件
   * @param {Object} data - 导出参数（与查询参数相同，用于筛选导出数据）
   * @param {string} [data.institutionName] - 机构名称筛选条件
   * @param {string} [data.operatorName] - 运营商筛选条件
   * @param {string} [data.staffName] - 负责人筛选条件
   * @param {string} [data.status] - 状态筛选条件
   * @returns {Promise<Object>} 返回导出结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {string} returns.data - 导出文件下载链接或文件内容
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导出所有结算人员与运营商维护信息
   * const result = await staffMaintenanceApi.export({
   *   institutionName: '测试机构'
   * });
   */
  export(data) {
    return request({
      url: "/st/xdt/charge/staffMaintenance/exportExcel",
      method: "post",
      data: data,
    });
  },

  /**
   * 导入结算人员与运营商维护信息Excel文件
   * @param {FormData} data - 包含文件的FormData对象
   * @param {File} data.file - 要导入的Excel文件（必填）
   * @returns {Promise<Object>} 返回导入结果
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息（包含导入成功/失败信息）
   * @returns {string} returns.data - 导入结果详情
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 导入Excel文件
   * const formData = new FormData();
   * formData.append('file', file);
   * const result = await staffMaintenanceApi.import(formData);
   */
  import(data) {
    return request({
      url: "/st/xdt/charge/staffMaintenance/importExcel",
      method: "post",
      data: data,
      headers: {
        "Content-Type": "multipart/form-data",
      },
      skipIntercept: true,
    });
  },

  /**
   * 获取结算人员与运营商维护相关的下拉列表数据
   * 用于表单选择器的数据源
   * @returns {Promise<Object>} 返回下拉列表数据
   * @returns {boolean} returns.success - 请求是否成功
   * @returns {string} returns.code - 响应状态码
   * @returns {string} returns.message - 响应消息
   * @returns {Object} returns.data - 下拉列表数据对象，key为字段名，value为选项数组
   * @returns {Array<string>} [returns.data.institutionNames] - 机构名称选项列表
   * @returns {Array<string>} [returns.data.operatorNames] - 运营商名称选项列表
   * @returns {Array<string>} [returns.data.staffNames] - 负责人选项列表
   * @returns {string} returns.traceId - 请求追踪ID
   * @example
   * // 获取下拉列表数据
   * const result = await staffMaintenanceApi.getDropLists();
   * // result.data = {
   * //   institutionNames: ['机构1', '机构2'],
   * //   operatorNames: ['运营商A', '运营商B'],
   * //   staffNames: ['张三', '李四']
   * // }
   */
  getDropLists() {
    return request({
      url: "/st/xdt/charge/staffMaintenance/getDropLists",
      method: "get",
    });
  },
};
