# 开发环境配置
ENV = 'development'

VUE_APP_NAME = "development"

VUE_APP_BASE_API = '/charging-maintenance-server'
VUE_APP_BASE_API_URL = '/charging-flow-test'
VUE_APP_BASE_API_PROCESS = "https://test-napi.bangdao-tech.com/charging-maintenance-flow"

#工作流上传图片接口
VUE_APP_BASE_UPLOAD_URL = "https://test-napi.bangdao-tech.com/charging-maintenance-server/flow-docking/api/v1/files/upload"

VUE_APP_WEBSOCKET_URL = 'wss://test-napi.bangdao-tech.com/jnsw-project-test/webSocket'

VUE_APP_WHALE_FLOW_KEY = 'SF-CM'

VUE_APP_TRACK_PROJECT_ID = 'PJ1712628237001'
VUE_APP_TRACK_REPORT_URL = "https://test-napi.bangdao-tech.com/monitor/collect"

# Mock系统配置
VUE_APP_ENABLE_MOCK = true